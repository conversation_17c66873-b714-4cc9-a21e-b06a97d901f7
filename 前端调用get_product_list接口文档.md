# 前端调用 get_product_list 接口完整指南

## 接口概述

`get_product_list` 是一个用于获取商品列表数据的用户端API接口，支持按分类筛选和分页功能，返回指定分类下的商品列表信息。

## 接口基本信息

- **接口路径**: `/user/api/productlist/`
- **请求方法**: `POST`
- **认证方式**: JWT Token + MD5签名双重验证
- **内容类型**: `application/x-www-form-urlencoded` 或 `multipart/form-data`
- **分页支持**: 是（默认每页20条，最大100条）

## 前端调用流程

### 1. 前置条件检查

在调用接口前，前端需要确保以下数据已存储在本地：

```javascript
// 检查必需的用户认证信息
const userId = localStorage.getItem('userId');        // 用户ID
const userKey = localStorage.getItem('userKey');      // 用户密钥
const token = localStorage.getItem('token');          // JWT访问令牌

// 处理userId和userKey以及token缺失的情况
if (!userId || !userKey || !token) 
    // 需要重新获取认证信息
    // 可以调用/user/api/GetUser/接口获取
    // 取响应头中的token作为请求附带的token
    // 取响应体中的user.id作为userId
    // 取响应体中的user.user_key作为userKey
```

### 2. MD5签名生成

接口需要MD5签名验证，签名算法为：`MD5(categoryId + userId + userKey)`

```javascript
// MD5签名生成函数
function generateProductListSignature(categoryId, userId, userKey) {
    const signData = categoryId + userId + userKey;
    return md5(signData);
}

// 生成签名
const sign = generateProductListSignature(categoryId, userId, userKey);
```

### 3. 请求参数构建

```javascript
// 构建POST请求参数
const formData = new FormData();
formData.append('categoryId', categoryId);    // 必需：分类ID（可为空字符串获取所有商品）
formData.append('userId', userId);            // 必需：用户ID
formData.append('sign', sign);                // 必需：MD5签名

// 分页参数
formData.append('page', page || 1);           // 可选：页码，默认1
formData.append('page_size', pageSize || 20); // 可选：每页数量，默认20，最大100

// 排序参数（如果后端支持）
if (sort && sort !== 'default') {
    formData.append('sort', sort);
}
```

### 5. 请求头配置

```javascript
const requestOptions = {
    method: 'POST',
    body: formData,
    headers: {
        'Token': token  // JWT令牌放在请求头中
    }
};
```

### 6. 发送请求

```javascript
fetch('/user/api/productlist/', requestOptions)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        handleProductListResponse(data);
    })
    .catch(error => {
        console.error('请求失败:', error);
        // 处理错误
    });
```

## 完整的前端调用示例

```javascript
async function getProductListData(categoryId = '', page = 1, pageSize = 20, sort = 'default') {
    try {
        // 1. 检查认证信息
        const userId = localStorage.getItem('userId');
        const userKey = localStorage.getItem('userKey');
        const token = localStorage.getItem('token');
        
        if (!userId || !userKey || !token) {
            throw new Error('用户认证信息缺失，请重新登录');
        }
        
        // 2. 生成MD5签名
        const signData = categoryId + userId + userKey;
        const sign = md5(signData);
        
        // 3. 构建请求参数
        const formData = new FormData();
        formData.append('categoryId', categoryId);
        formData.append('userId', userId);
        formData.append('sign', sign);
        formData.append('page', page);
        formData.append('page_size', Math.min(pageSize, 100)); // 限制最大100条
        
        if (sort && sort !== 'default') {
            formData.append('sort', sort);
        }
        
        // 4. 配置请求选项
        const requestOptions = {
            method: 'POST',
            body: formData,
            headers: {
                'Token': token
            }
        };
        
        // 5. 发送请求
        const response = await fetch('/user/api/productlist/', requestOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }
        
        const data = await response.json();
        
        // 6. 处理响应
        if (data.code === 200 && data.data) {
            console.log('商品列表获取成功:', data.data);
            return data.data;
        } else {
            throw new Error(data.msg || '获取商品列表失败');
        }
        
    } catch (error) {
        console.error('获取商品列表失败:', error);
        throw error;
    }
}
```

## 响应数据格式

### 成功响应 (HTTP 200)

```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "iPhone 15 Pro",
                "price": "7999.00",
                "image": "/media/products/iphone15pro.jpg",
                "image_type": "2",
                "PriceTemplate": 1,
                "type": "1",
                "sales_count": 156,
                "category": "11",
                "info": "最新款iPhone，性能强劲",
                "attach": [
                    {
                        "name": "颜色",
                        "value": "深空黑色"
                    }
                ],
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-20T15:45:00Z",
                "status": "1"
            }
        ],
        "pagination": {
            "current_page": 1,
            "page_size": 20,
            "total": 156,
            "total_pages": 8,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

### 错误响应

#### 参数不完整 (HTTP 400)
```json
{
    "code": 400,
    "msg": "参数不完整"
}
```

#### 参数错误 (HTTP 400)
```json
{
    "code": 400,
    "msg": "参数错误: [具体错误信息]"
}
```

#### 签名验证失败 (HTTP 403)
```json
{
    "code": 403,
    "msg": "签名验证失败"
}
```

#### Token相关错误 (HTTP 401)
```json
{
    "code": 401,
    "msg": "未授权访问，缺少Token"
}
```

#### 服务器错误 (HTTP 500)
```json
{
    "code": 500,
    "msg": "服务器错误: [具体错误信息]"
}
```

## 数据结构说明

### 商品对象 (Product Item)
```typescript
interface ProductItem {
    id: number;              // 商品ID
    name: string;            // 商品名称
    price: string;           // 商品价格（实际价格，已计算会员折扣）
    image: string;           // 商品图片URL
    image_type: string;      // 图片类型
    PriceTemplate: number;   // 价格模板ID
    type: string;            // 商品类型（1:普通商品 2:卡密商品 3:对接商品）
    sales_count: number;     // 销售数量
    category: string;        // 分类ID
    info: string;            // 商品描述
    attach: Array<{          // 商品附加属性
        name: string;        // 属性名
        value: string;       // 属性值
    }>;
    created_at: string;      // 创建时间
    updated_at: string;      // 更新时间
    status: string;          // 商品状态
}
```

### 分页对象 (Pagination)
```typescript
interface Pagination {
    current_page: number;    // 当前页码
    page_size: number;       // 每页数量
    total: number;           // 总记录数
    total_pages: number;     // 总页数
    has_next: boolean;       // 是否有下一页
    has_prev: boolean;       // 是否有上一页
}
```

## 请求参数详解

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| categoryId | string | 是 | 分类ID，空字符串表示获取所有商品 |
| userId | string | 是 | 用户ID |
| sign | string | 是 | MD5签名，算法：MD5(categoryId + userId + userKey) |
| page | integer | 否 | 页码，默认1，最小1 |
| page_size | integer | 否 | 每页数量，默认20，最大100 |
| sort | string | 否 | 排序方式（如果后端支持） |

## 错误处理建议

```javascript
function handleProductListError(error, data) {
    if (data) {
        switch (data.code) {
            case 400:
                console.error('请求参数错误:', data.msg);
                showError('请求参数有误，请检查分类ID和分页参数');
                break;
            case 401:
                console.error('认证失败:', data.msg);
                // 重新获取用户信息
                break;
            case 403:
                console.error('签名验证失败:', data.msg);
                showError('签名验证失败，请重新登录');
                break;
            case 500:
                console.error('服务器错误:', data.msg);
                showError('服务器繁忙，请稍后重试');
                break;
            default:
                console.error('未知错误:', data);
                showError('获取商品列表失败，请重试');
        }
    } else {
        console.error('网络错误:', error);
        showError('网络连接失败，请检查网络后重试');
    }
}
```

## 特殊功能说明

### 1. 对接商品自动更新
- 接口会自动识别对接商品（type为"3"）
- 后台会异步更新对接商品的最新信息
- 不会阻塞前端响应，提升用户体验

### 2. 图片处理
- 如果商品没有图片或图片URL为空，会自动生成占位图片URL
- 格式：`/api/get_local_image_product?id={商品ID}&placeholder=true`

### 3. 价格计算
- 返回的price字段是根据用户会员等级计算后的实际价格
- 已考虑价格模板和会员折扣

## 注意事项

1. **签名算法**: 严格按照 `MD5(categoryId + userId + userKey)` 生成签名
2. **分页限制**: 每页最大100条记录，超出会被自动限制
3. **分类ID**: 可以传空字符串获取所有商品
4. **性能优化**: 建议合理设置页面大小，避免一次性加载过多数据
5. **缓存策略**: 可考虑对商品列表进行适当缓存
6. **错误重试**: 网络错误时建议实现重试机制

## 依赖库

前端需要引入MD5加密库：
```html
<script src="https://cdn.jsdelivr.net/npm/js-md5@0.7.3/build/md5.min.js"></script>
```
